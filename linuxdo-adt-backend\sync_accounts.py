#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号同步脚本
功能：
1. 自动获取和管理 API token
2. 获取系统中的账号信息
3. 维护 user_info.txt 文件
"""

import os
import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional


class AccountSyncManager:
    def __init__(self, base_url: str = "http://localhost:8000", 
                 username: str = "admin", password: str = "admin123"):
        """
        初始化账号同步管理器
        
        Args:
            base_url: API 基础URL
            username: 管理员用户名
            password: 管理员密码
        """
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.token_file = "token_cache.json"
        self.user_info_file = "user_info.txt"
        self.token_data = None
        
    def load_token_cache(self) -> Optional[Dict]:
        """从缓存文件加载token"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 检查token是否过期（提前5分钟刷新）
                    expire_time = datetime.fromisoformat(data['expire_time'])
                    if datetime.now() + timedelta(minutes=5) < expire_time:
                        return data
        except Exception as e:
            print(f"加载token缓存失败: {e}")
        return None
    
    def save_token_cache(self, token: str, expire_minutes: int = 30):
        """保存token到缓存文件"""
        try:
            expire_time = datetime.now() + timedelta(minutes=expire_minutes)
            data = {
                'token': token,
                'expire_time': expire_time.isoformat(),
                'created_at': datetime.now().isoformat()
            }
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.token_data = data
            print(f"Token已保存，过期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}")
        except Exception as e:
            print(f"保存token缓存失败: {e}")
    
    def get_valid_token(self) -> Optional[str]:
        """获取有效的token"""
        # 先尝试从缓存加载
        cached_token = self.load_token_cache()
        if cached_token:
            self.token_data = cached_token
            print("使用缓存的token")
            return cached_token['token']
        
        # 缓存无效，重新登录获取token
        return self.login_and_get_token()
    
    def login_and_get_token(self) -> Optional[str]:
        """登录并获取新的token"""
        try:
            print("正在登录获取新token...")
            login_url = f"{self.base_url}/api/auth/login"
            
            # 准备登录数据
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            # 发送登录请求
            response = requests.post(login_url, data=login_data)
            response.raise_for_status()
            
            result = response.json()
            token = result.get('access_token')
            
            if token:
                # 保存token到缓存
                self.save_token_cache(token)
                print("登录成功，获取到新token")
                return token
            else:
                print("登录响应中没有找到token")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"登录请求失败: {e}")
            return None
        except Exception as e:
            print(f"登录过程出错: {e}")
            return None
    
    def get_accounts(self) -> List[Dict]:
        """获取所有账号信息"""
        token = self.get_valid_token()
        if not token:
            print("无法获取有效token")
            return []
        
        try:
            print("正在获取账号信息...")
            accounts_url = f"{self.base_url}/api/accounts"
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(accounts_url, headers=headers)
            response.raise_for_status()
            
            accounts = response.json()
            print(f"获取到 {len(accounts)} 个账号")
            return accounts
            
        except requests.exceptions.RequestException as e:
            print(f"获取账号信息失败: {e}")
            return []
        except Exception as e:
            print(f"处理账号信息时出错: {e}")
            return []
    
    def load_existing_user_info(self) -> Dict[str, str]:
        """加载现有的user_info.txt文件中的用户信息"""
        existing_users = {}
        try:
            if os.path.exists(self.user_info_file):
                with open(self.user_info_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # CSV格式: username,password,email,notify_flag
                            parts = line.split(',')
                            if parts and len(parts) >= 2:
                                username = parts[0].strip()
                                existing_users[username] = line
                print(f"从现有文件中加载了 {len(existing_users)} 个用户")
        except Exception as e:
            print(f"加载现有用户信息失败: {e}")

        return existing_users
    
    def update_user_info_file(self, accounts: List[Dict]):
        """更新user_info.txt文件"""
        try:
            # 加载现有用户信息
            existing_users = self.load_existing_user_info()

            # 获取系统中的账号信息
            system_accounts = {}
            active_accounts = {}

            for account in accounts:
                username = account.get('username', '')
                system_accounts[username] = account

                # 进行中状态的账号
                if account.get('status') == '处理中':
                    active_accounts[username] = account

            print(f"系统中有 {len(system_accounts)} 个账号，其中 {len(active_accounts)} 个进行中")

            # 构建最终的用户列表
            final_users = {}

            # 1. 添加系统中进行中状态的账号
            for username, account in active_accounts.items():
                # 转换为CSV格式: username,password,email,notify_flag
                notify_flag = "1" if account.get('need_notification', True) else "0"
                line = f"{account.get('username', '')},{account.get('password', '')},{account.get('email', '')},{notify_flag}"
                final_users[username] = line

            # 2. 保留文件中存在但不在系统中的账号
            for username, line in existing_users.items():
                if username not in system_accounts:
                    final_users[username] = line

            # 3. 移除系统中非进行中状态的账号（这些账号在系统中但状态不是进行中）
            removed_count = 0
            for username in existing_users:
                if username in system_accounts and username not in active_accounts:
                    removed_count += 1

            print(f"将保留 {len(final_users)} 个用户")
            print(f"移除了 {removed_count} 个非进行中状态的账号")
            
            # 重新生成user_info.txt文件
            with open(self.user_info_file, 'w', encoding='utf-8') as f:
                # 写入文件头注释
                f.write("# Linux.do多用户配置文件\n")
                f.write("# 格式: username,password,email,notify_flag\n")
                f.write("# 每行一个用户，至少需要用户名和密码\n")
                f.write("# 邮箱：如需接收邮件通知，请填写正确的邮箱地址\n")
                f.write("# notify_flag：1=接收个人通知邮件，0=不接收个人通知邮件\n")
                f.write("# 注释行以#开头\n")
                f.write(f"# 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 总计: {len(final_users)} 个账号\n")
                f.write("\n")

                # 写入账号信息（按用户名排序）
                for username in sorted(final_users.keys()):
                    f.write(final_users[username] + '\n')

            print(f"user_info.txt 文件已更新")
            
        except Exception as e:
            print(f"更新用户信息文件失败: {e}")
    

    def run_sync(self):
        """执行同步操作"""
        print("=" * 50)
        print(f"开始账号同步 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        try:
            # 获取账号信息
            accounts = self.get_accounts()
            
            if accounts:
                # 更新user_info.txt文件
                self.update_user_info_file(accounts)
                print("同步完成")
            else:
                print("未获取到账号信息，跳过同步")
                
        except Exception as e:
            print(f"同步过程中出错: {e}")
        
        print("=" * 50)


def main():
    """主函数"""
    # 配置信息
    config = {
        'base_url': 'http://localhost:8000',
        'username': 'admin',
        'password': 'admin123'
    }
    
    # 创建同步管理器
    sync_manager = AccountSyncManager(**config)
    
    # 执行同步
    sync_manager.run_sync()


if __name__ == "__main__":
    main()
