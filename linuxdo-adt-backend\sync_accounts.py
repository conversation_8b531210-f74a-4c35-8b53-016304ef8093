#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号同步脚本
功能：
1. 自动获取和管理 API token
2. 获取系统中的账号信息
3. 维护 user_info.txt 文件
"""

import os
import json
import time
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Set, Optional


class AccountSyncManager:
    def __init__(self, base_url: str = "http://localhost:8000", 
                 username: str = "admin", password: str = "admin123"):
        """
        初始化账号同步管理器
        
        Args:
            base_url: API 基础URL
            username: 管理员用户名
            password: 管理员密码
        """
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.token_file = "token_cache.json"
        self.user_info_file = "user_info.txt"
        self.token_data = None
        
    def load_token_cache(self) -> Optional[Dict]:
        """从缓存文件加载token"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 检查token是否过期（提前5分钟刷新）
                    expire_time = datetime.fromisoformat(data['expire_time'])
                    if datetime.now() + timedelta(minutes=5) < expire_time:
                        return data
        except Exception as e:
            print(f"加载token缓存失败: {e}")
        return None
    
    def save_token_cache(self, token: str, expire_minutes: int = 30):
        """保存token到缓存文件"""
        try:
            expire_time = datetime.now() + timedelta(minutes=expire_minutes)
            data = {
                'token': token,
                'expire_time': expire_time.isoformat(),
                'created_at': datetime.now().isoformat()
            }
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.token_data = data
            print(f"Token已保存，过期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}")
        except Exception as e:
            print(f"保存token缓存失败: {e}")
    
    def get_valid_token(self) -> Optional[str]:
        """获取有效的token"""
        # 先尝试从缓存加载
        cached_token = self.load_token_cache()
        if cached_token:
            self.token_data = cached_token
            print("使用缓存的token")
            return cached_token['token']
        
        # 缓存无效，重新登录获取token
        return self.login_and_get_token()
    
    def login_and_get_token(self) -> Optional[str]:
        """登录并获取新的token"""
        try:
            print("正在登录获取新token...")
            login_url = f"{self.base_url}/api/auth/login"
            
            # 准备登录数据
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            # 发送登录请求
            response = requests.post(login_url, data=login_data)
            response.raise_for_status()
            
            result = response.json()
            token = result.get('access_token')
            
            if token:
                # 保存token到缓存
                self.save_token_cache(token)
                print("登录成功，获取到新token")
                return token
            else:
                print("登录响应中没有找到token")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"登录请求失败: {e}")
            return None
        except Exception as e:
            print(f"登录过程出错: {e}")
            return None
    
    def get_accounts(self) -> List[Dict]:
        """获取所有账号信息"""
        token = self.get_valid_token()
        if not token:
            print("无法获取有效token")
            return []
        
        try:
            print("正在获取账号信息...")
            accounts_url = f"{self.base_url}/api/accounts"
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(accounts_url, headers=headers)
            response.raise_for_status()
            
            accounts = response.json()
            print(f"获取到 {len(accounts)} 个账号")
            return accounts
            
        except requests.exceptions.RequestException as e:
            print(f"获取账号信息失败: {e}")
            return []
        except Exception as e:
            print(f"处理账号信息时出错: {e}")
            return []
    
    def load_existing_user_info(self) -> Set[str]:
        """加载现有的user_info.txt文件中的用户名"""
        existing_users = set()
        try:
            if os.path.exists(self.user_info_file):
                with open(self.user_info_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            # 假设每行格式为: username:password:email 或其他格式
                            # 这里提取用户名（第一个字段）
                            parts = line.split(':')
                            if parts:
                                existing_users.add(parts[0])
                print(f"从现有文件中加载了 {len(existing_users)} 个用户")
        except Exception as e:
            print(f"加载现有用户信息失败: {e}")
        
        return existing_users
    
    def update_user_info_file(self, accounts: List[Dict]):
        """更新user_info.txt文件"""
        try:
            # 加载现有用户信息
            existing_users = self.load_existing_user_info()
            
            # 获取系统中进行中状态的账号
            active_accounts = {}
            all_system_users = set()
            
            for account in accounts:
                username = account.get('username', '')
                all_system_users.add(username)
                
                # 只处理进行中状态的账号
                if account.get('status') == '处理中':
                    active_accounts[username] = account
            
            print(f"系统中有 {len(active_accounts)} 个进行中状态的账号")
            
            # 需要保留的用户：现有文件中的用户 + 系统中进行中的账号
            users_to_keep = existing_users.copy()
            
            # 添加系统中进行中的账号
            for username in active_accounts:
                users_to_keep.add(username)
            
            # 移除系统中非进行中状态的账号（但保留不在系统中的账号）
            users_to_remove = set()
            for username in existing_users:
                if username in all_system_users:
                    # 如果用户在系统中，但不是进行中状态，则移除
                    if username not in active_accounts:
                        users_to_remove.add(username)
            
            users_to_keep -= users_to_remove
            
            print(f"将保留 {len(users_to_keep)} 个用户")
            print(f"将移除 {len(users_to_remove)} 个用户")
            
            # 重新生成user_info.txt文件
            with open(self.user_info_file, 'w', encoding='utf-8') as f:
                # 写入文件头注释
                f.write(f"# 账号信息文件\n")
                f.write(f"# 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# 格式: username:password:email:level_info:key_info:need_notification\n")
                f.write(f"#\n")
                
                # 写入账号信息
                for username in sorted(users_to_keep):
                    if username in active_accounts:
                        # 系统中的进行中账号，使用最新信息
                        account = active_accounts[username]
                        line = f"{account.get('username', '')}:{account.get('password', '')}:{account.get('email', '')}:{account.get('level_info', '')}:{account.get('key_info', '')}:{account.get('need_notification', True)}"
                    else:
                        # 文件中已存在但不在系统中的账号，保持原有格式
                        # 这里需要从原文件中读取原始信息
                        line = self.get_original_user_line(username)
                    
                    if line:
                        f.write(line + '\n')
            
            print(f"user_info.txt 文件已更新")
            
        except Exception as e:
            print(f"更新用户信息文件失败: {e}")
    
    def get_original_user_line(self, username: str) -> str:
        """从原文件中获取用户的原始行信息"""
        try:
            if os.path.exists(self.user_info_file):
                with open(self.user_info_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            if line.split(':')[0] == username:
                                return line
        except Exception as e:
            print(f"获取原始用户信息失败: {e}")
        
        # 如果找不到原始信息，返回基本格式
        return f"{username}::::True"
    
    def run_sync(self):
        """执行同步操作"""
        print("=" * 50)
        print(f"开始账号同步 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        try:
            # 获取账号信息
            accounts = self.get_accounts()
            
            if accounts:
                # 更新user_info.txt文件
                self.update_user_info_file(accounts)
                print("同步完成")
            else:
                print("未获取到账号信息，跳过同步")
                
        except Exception as e:
            print(f"同步过程中出错: {e}")
        
        print("=" * 50)


def main():
    """主函数"""
    # 配置信息
    config = {
        'base_url': 'http://localhost:8000',
        'username': 'admin',
        'password': 'admin123'
    }
    
    # 创建同步管理器
    sync_manager = AccountSyncManager(**config)
    
    # 执行同步
    sync_manager.run_sync()


if __name__ == "__main__":
    main()
