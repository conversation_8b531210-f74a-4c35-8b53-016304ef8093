# 账号同步脚本使用说明

## 脚本概览

本文件夹包含两个主要脚本：

1. **sync_accounts.py** - 账号信息同步脚本
2. **update_user_info.py** - 用户信息更新脚本（新增）

## 功能概述

这个脚本用于自动同步 LinuxDo ADT 系统中的账号信息，并维护一个 `user_info.txt` 文件。

### 主要功能

1. **自动Token管理**：自动获取和缓存API访问token，过期时自动刷新
2. **账号信息同步**：从系统API获取所有账号信息
3. **智能文件维护**：
   - 系统中"进行中"状态的账号会被添加到文件中
   - 系统中非"进行中"状态的账号会从文件中删除
   - 文件中存在但系统中不存在的账号会被保留（防止误删）

## 文件结构

```
linuxdo-adt-backend/sync_accounts/
├── sync_accounts.py         # 账号同步脚本
├── update_user_info.py      # 用户信息更新脚本（新增）
├── update_user_info.bat     # Windows批处理脚本
├── config_sync.py          # 配置文件
├── README_sync.md          # 使用说明
├── user_info_example.txt   # 用户信息示例文件
├── user_api_info.txt       # 用户API信息文件（新增）
├── token_cache.json        # Token缓存文件（自动生成）
└── user_info.txt           # 用户信息文件（自动生成/更新）
```

## 使用方法

### 1. 配置设置

编辑 `config_sync.py` 文件，修改以下配置：

```python
API_CONFIG = {
    'base_url': 'http://localhost:8000',  # 修改为实际的API地址
    'username': 'admin',                  # 修改为实际的管理员用户名
    'password': 'admin123'                # 修改为实际的管理员密码
}
```

### 2. 安装依赖

```bash
pip install requests
```

### 3. 运行脚本

```bash
# 进入后端目录
cd linuxdo-adt-backend

# 运行同步脚本
python sync_accounts.py
```

### 4. 定时运行（可选）

可以使用系统的定时任务来定期运行脚本：

#### Linux/macOS (crontab)
```bash
# 编辑crontab
crontab -e

# 添加定时任务（每小时运行一次）
0 * * * * cd /path/to/linuxdo-adt-backend && python sync_accounts.py
```

#### Windows (任务计划程序)
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器（如每小时运行一次）
4. 设置操作：启动程序 `python`，参数 `sync_accounts.py`，起始于脚本目录

## 输出文件格式

### user_info.txt 格式

```
# 账号信息文件
# 更新时间: 2024-01-01 12:00:00
# 格式: username:password:email:level_info:key_info:need_notification
# 总计: 5 个账号
#
testuser1:password123:<EMAIL>:2级:key123:True
testuser2:password456:<EMAIL>:3级:key456:False
```

### token_cache.json 格式

```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expire_time": "2024-01-01T13:00:00.000000",
  "created_at": "2024-01-01T12:30:00.000000"
}
```

## 运行日志示例

```
==================================================
开始账号同步 - 2024-01-01 12:00:00
==================================================
使用缓存的token
正在获取账号信息...
获取到 10 个账号
从现有文件中加载了 8 个用户
系统中有 10 个账号，其中 6 个进行中
将保留 9 个用户
移除了 2 个非进行中状态的账号
user_info.txt 文件已更新
同步完成
==================================================
```

## 同步逻辑说明

脚本的同步逻辑如下：

1. **获取系统账号**：通过API获取所有账号信息
2. **分类处理**：
   - `进行中状态账号`：需要保留在文件中
   - `非进行中状态账号`：需要从文件中删除
   - `文件中已存在但系统中不存在的账号`：保留（防止误删）

3. **文件更新**：
   - 添加系统中所有"进行中"状态的账号
   - 保留文件中存在但系统中不存在的账号
   - 移除系统中存在但状态非"进行中"的账号

## 注意事项

1. **权限要求**：脚本需要使用管理员账号才能访问账号信息API
2. **网络连接**：确保脚本运行环境能够访问API服务器
3. **文件权限**：确保脚本有权限读写工作目录中的文件
4. **备份建议**：建议定期备份 `user_info.txt` 文件
5. **安全性**：配置文件中包含密码，请妥善保管

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名和密码是否正确
   - 确认API服务器是否正常运行

2. **Token过期**
   - 脚本会自动处理token过期，重新登录获取新token

3. **网络连接问题**
   - 检查API服务器地址是否正确
   - 确认网络连接是否正常

4. **文件权限问题**
   - 确保脚本有权限读写当前目录
   - 检查文件是否被其他程序占用

### 调试模式

如需启用详细日志，可以修改 `config_sync.py` 中的 `LOG_CONFIG`：

```python
LOG_CONFIG = {
    'enable_debug': True,      # 启用调试日志
    'log_file': 'sync.log'     # 写入日志文件
}
```

---

## 用户信息更新脚本 (update_user_info.py)

### 功能说明

这个脚本用于读取 `user_api_info.txt` 文件，并调用系统接口更新用户的等级信息和API Key。

### 主要功能

1. **读取API信息文件**：解析 `user_api_info.txt` 文件中的用户信息
2. **查找系统用户**：根据用户名在系统中查找对应的账号
3. **更新用户信息**：更新用户的等级信息和API Key
4. **跳过不存在用户**：对于系统中不存在的用户名，自动跳过

### 使用方法

#### 1. 准备API信息文件

创建或编辑 `user_api_info.txt` 文件：

```
# 用户名,信任等级,API_Key
# 格式: username,trust_level,api_key
# 此文件由脚本自动生成和维护，每次代挂完成后会自动更新
# 示例:
# user1,3级,4VD3PkXAe8llZ_Df517yJnZKIbc64bB75ue5XcOvRpw
# user2,2级,XyZ123AbC456DeF789GhI012JkL345MnO678PqR901StU

user1,3级,4VD3PkXAe8llZ_Df517yJnZKIbc64bB75ue5XcOvRpw
user2,2级,XyZ123AbC456DeF789GhI012JkL345MnO678PqR901StU
testuser,4级,AbC123DeF456GhI789JkL012MnO345PqR678StU901VwX
```

#### 2. 运行脚本

**方法一：直接运行Python脚本**
```bash
cd linuxdo-adt-backend/sync_accounts
python update_user_info.py
```

**方法二：使用批处理脚本（Windows）**
```bash
cd linuxdo-adt-backend/sync_accounts
update_user_info.bat
```

### 运行日志示例

```
============================================================
开始用户信息更新 - 2024-01-01 12:00:00
============================================================
使用缓存的token
从文件中加载了 3 个用户信息

处理用户: user1
  ✅ 用户 user1 更新成功
     等级: 3级
     API Key: 4VD3PkXAe8llZ_Df517y...

处理用户: user2
  ✅ 用户 user2 更新成功
     等级: 2级
     API Key: XyZ123AbC456DeF789Gh...

处理用户: nonexistent_user
  ❌ 用户 nonexistent_user 不存在，跳过

============================================================
更新完成统计:
  成功更新: 2 个用户
  跳过用户: 1 个用户
  更新失败: 0 个用户
============================================================
```

### 文件格式说明

#### user_api_info.txt 格式

- **username**: LinuxDo用户名
- **trust_level**: 信任等级（如：1级、2级、3级、4级等）
- **api_key**: API密钥

### 注意事项

1. **文件格式**：确保 `user_api_info.txt` 使用正确的CSV格式
2. **用户名匹配**：只有系统中存在的用户名才会被更新
3. **权限要求**：需要管理员权限才能更新用户信息
4. **数据保护**：只更新等级和API Key，其他信息保持不变
5. **错误处理**：脚本会自动跳过不存在的用户，不会中断执行

### 配置说明

脚本中的配置与同步脚本相同，需要修改 `update_user_info.py` 中的配置：

```python
config = {
    'base_url': 'http://localhost:8000',  # API服务器地址
    'username': 'admin',                  # 管理员用户名
    'password': 'admin123'                # 管理员密码
}
```
