#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号同步脚本测试工具
用于测试同步脚本的各项功能
"""

import os
import json
from datetime import datetime
from sync_accounts import AccountSyncManager


def test_token_management():
    """测试Token管理功能"""
    print("=" * 50)
    print("测试Token管理功能")
    print("=" * 50)
    
    # 创建同步管理器
    sync_manager = AccountSyncManager()
    
    # 测试获取token
    token = sync_manager.get_valid_token()
    if token:
        print(f"✓ Token获取成功: {token[:20]}...")
        
        # 检查token缓存文件
        if os.path.exists(sync_manager.token_file):
            print("✓ Token缓存文件已创建")
            with open(sync_manager.token_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                print(f"✓ Token过期时间: {cache_data['expire_time']}")
        else:
            print("✗ Token缓存文件未创建")
    else:
        print("✗ Token获取失败")
    
    print()


def test_account_fetching():
    """测试账号信息获取"""
    print("=" * 50)
    print("测试账号信息获取")
    print("=" * 50)
    
    sync_manager = AccountSyncManager()
    accounts = sync_manager.get_accounts()
    
    if accounts:
        print(f"✓ 成功获取 {len(accounts)} 个账号")
        
        # 显示账号状态统计
        status_count = {}
        for account in accounts:
            status = account.get('status', '未知')
            status_count[status] = status_count.get(status, 0) + 1
        
        print("账号状态统计:")
        for status, count in status_count.items():
            print(f"  - {status}: {count} 个")
    else:
        print("✗ 未获取到账号信息")
    
    print()
    return accounts


def test_file_operations():
    """测试文件操作功能"""
    print("=" * 50)
    print("测试文件操作功能")
    print("=" * 50)
    
    sync_manager = AccountSyncManager()
    
    # 创建测试用的user_info.txt文件
    test_content = """# 测试账号信息文件
# 更新时间: 2024-01-01 12:00:00
# 格式: username:password:email:level_info:key_info:need_notification
#
testuser1:password123:<EMAIL>:2级:key123:True
testuser2:password456:<EMAIL>:3级:key456:False
olduser:oldpass:<EMAIL>:::True
"""
    
    # 备份现有文件（如果存在）
    backup_file = None
    if os.path.exists(sync_manager.user_info_file):
        backup_file = f"{sync_manager.user_info_file}.backup"
        os.rename(sync_manager.user_info_file, backup_file)
        print(f"✓ 已备份现有文件到: {backup_file}")
    
    # 创建测试文件
    with open(sync_manager.user_info_file, 'w', encoding='utf-8') as f:
        f.write(test_content)
    print("✓ 创建测试用户信息文件")
    
    # 测试加载现有用户信息
    existing_users = sync_manager.load_existing_user_info()
    print(f"✓ 加载到 {len(existing_users)} 个现有用户")
    for username in existing_users:
        print(f"  - {username}")
    
    # 恢复备份文件
    if backup_file and os.path.exists(backup_file):
        os.remove(sync_manager.user_info_file)
        os.rename(backup_file, sync_manager.user_info_file)
        print(f"✓ 已恢复原始文件")
    
    print()


def test_full_sync():
    """测试完整同步流程"""
    print("=" * 50)
    print("测试完整同步流程")
    print("=" * 50)
    
    sync_manager = AccountSyncManager()
    
    # 备份现有文件
    backup_file = None
    if os.path.exists(sync_manager.user_info_file):
        backup_file = f"{sync_manager.user_info_file}.backup.{int(datetime.now().timestamp())}"
        os.rename(sync_manager.user_info_file, backup_file)
        print(f"✓ 已备份现有文件到: {backup_file}")
    
    try:
        # 执行同步
        sync_manager.run_sync()
        
        # 检查结果
        if os.path.exists(sync_manager.user_info_file):
            print("✓ 同步完成，user_info.txt 文件已生成")
            
            # 显示文件内容摘要
            with open(sync_manager.user_info_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                user_lines = [line for line in lines if line.strip() and not line.startswith('#')]
                print(f"✓ 文件包含 {len(user_lines)} 个用户记录")
        else:
            print("✗ 同步后未生成用户信息文件")
    
    except Exception as e:
        print(f"✗ 同步过程中出错: {e}")
    
    finally:
        # 询问是否恢复备份
        if backup_file and os.path.exists(backup_file):
            response = input("是否恢复原始文件？(y/n): ").lower().strip()
            if response == 'y':
                if os.path.exists(sync_manager.user_info_file):
                    os.remove(sync_manager.user_info_file)
                os.rename(backup_file, sync_manager.user_info_file)
                print("✓ 已恢复原始文件")
            else:
                print(f"备份文件保存在: {backup_file}")
    
    print()


def main():
    """主测试函数"""
    print("LinuxDo ADT 账号同步脚本测试工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 测试Token管理功能")
        print("2. 测试账号信息获取")
        print("3. 测试文件操作功能")
        print("4. 测试完整同步流程")
        print("5. 运行所有测试")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            print("退出测试工具")
            break
        elif choice == '1':
            test_token_management()
        elif choice == '2':
            test_account_fetching()
        elif choice == '3':
            test_file_operations()
        elif choice == '4':
            test_full_sync()
        elif choice == '5':
            test_token_management()
            test_account_fetching()
            test_file_operations()
            test_full_sync()
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
