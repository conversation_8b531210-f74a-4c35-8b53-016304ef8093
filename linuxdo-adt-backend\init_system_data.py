#!/usr/bin/env python3
"""
初始化系统数据脚本
添加默认的系统设置和邮件模板
"""

from app.database import SessionLocal, engine
from app.models import Base, SystemSettings, EmailTemplate, User
from app.auth import get_password_hash
from datetime import datetime, timezone, timedelta

# 定义东八区时区
CHINA_TZ = timezone(timedelta(hours=8))

def init_system_settings():
    """初始化系统设置"""
    db = SessionLocal()
    try:
        # 默认系统设置
        default_settings = [
            {
                "key": "registration_enabled",
                "value": "true",
                "description": "是否允许用户注册"
            },
            {
                "key": "smtp_server",
                "value": "smtp.qq.com",
                "description": "SMTP服务器地址"
            },
            {
                "key": "smtp_port",
                "value": "587",
                "description": "SMTP服务器端口"
            },
            {
                "key": "smtp_username",
                "value": "",
                "description": "SMTP用户名"
            },
            {
                "key": "smtp_password",
                "value": "",
                "description": "SMTP密码"
            },
            {
                "key": "smtp_use_tls",
                "value": "true",
                "description": "是否使用TLS加密"
            },
            {
                "key": "from_email",
                "value": "",
                "description": "发件人邮箱"
            },
            {
                "key": "from_name",
                "value": "LinuxDo ADT",
                "description": "发件人名称"
            },
            {
                "key": "site_name",
                "value": "LinuxDo ADT",
                "description": "网站名称"
            },
            {
                "key": "site_url",
                "value": "http://localhost:5173",
                "description": "网站URL"
            },
            {
                "key": "show_homepage",
                "value": "true",
                "description": "是否显示首页介绍页面"
            }
        ]
        
        for setting_data in default_settings:
            # 检查是否已存在
            existing = db.query(SystemSettings).filter(
                SystemSettings.key == setting_data["key"]
            ).first()
            
            if not existing:
                setting = SystemSettings(**setting_data)
                db.add(setting)
                print(f"添加系统设置: {setting_data['key']}")
        
        db.commit()
        print("系统设置初始化完成")
        
    except Exception as e:
        print(f"初始化系统设置失败: {str(e)}")
        db.rollback()
    finally:
        db.close()


def init_email_templates():
    """初始化邮件模板"""
    db = SessionLocal()
    try:
        # 默认邮件模板
        default_templates = [
            {
                "name": "registration_welcome",
                "subject": "欢迎注册 {{ site_name }}",
                "content": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>欢迎注册</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">欢迎注册 {{ site_name }}！</h2>
        
        <p>亲爱的 {{ username }}，</p>
        
        <p>感谢您注册 {{ site_name }}！您的账户已经成功创建。</p>
        
        <p>您现在可以使用以下功能：</p>
        <ul>
            <li>发布代练任务</li>
            <li>查看任务进度</li>
            <li>管理账户信息</li>
        </ul>
        
        <p>
            <a href="{{ login_url }}" style="background-color: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                立即登录
            </a>
        </p>
        
        <p>如果您有任何问题，请随时联系我们。</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        <p style="font-size: 12px; color: #666;">
            此邮件由系统自动发送，请勿回复。
        </p>
    </div>
</body>
</html>
                """,
                "description": "用户注册欢迎邮件模板"
            },
            {
                "name": "task_notification",
                "subject": "任务通知: {{ task_title }}",
                "content": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>任务通知</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">任务通知</h2>
        
        <p>您好，</p>
        
        <p>您的任务有新的更新：</p>
        
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <h3 style="margin: 0 0 10px 0; color: #2c3e50;">{{ task_title }}</h3>
            <p style="margin: 0;"><strong>任务类型：</strong>{{ task_type }}</p>
        </div>
        
        <p>请登录 {{ site_name }} 查看详细信息。</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        <p style="font-size: 12px; color: #666;">
            此邮件由系统自动发送，请勿回复。
        </p>
    </div>
</body>
</html>
                """,
                "description": "任务状态变更通知邮件模板"
            },
            {
                "name": "password_reset",
                "subject": "密码重置 - {{ site_name }}",
                "content": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>密码重置</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <h2 style="color: #2c3e50;">密码重置请求</h2>
        
        <p>您好 {{ username }}，</p>
        
        <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
        
        <p>
            <a href="{{ reset_url }}" style="background-color: #e74c3c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                重置密码
            </a>
        </p>
        
        <p>此链接将在 24 小时后失效。</p>
        
        <p>如果您没有请求重置密码，请忽略此邮件。</p>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;">
        <p style="font-size: 12px; color: #666;">
            此邮件由系统自动发送，请勿回复。
        </p>
    </div>
</body>
</html>
                """,
                "description": "密码重置邮件模板"
            }
        ]
        
        for template_data in default_templates:
            # 检查是否已存在
            existing = db.query(EmailTemplate).filter(
                EmailTemplate.name == template_data["name"]
            ).first()
            
            if not existing:
                template = EmailTemplate(**template_data)
                db.add(template)
                print(f"添加邮件模板: {template_data['name']}")
        
        db.commit()
        print("邮件模板初始化完成")
        
    except Exception as e:
        print(f"初始化邮件模板失败: {str(e)}")
        db.rollback()
    finally:
        db.close()


def init_admin_user():
    """初始化管理员用户"""
    db = SessionLocal()
    try:
        # 检查是否已存在管理员用户
        admin_user = db.query(User).filter(User.username == "admin").first()

        if not admin_user:
            # 创建默认管理员用户
            admin_password = "admin123"  # 默认密码，建议首次登录后修改
            hashed_password = get_password_hash(admin_password)

            admin_user = User(
                username="admin",
                email="<EMAIL>",
                password_hash=hashed_password,
                role="admin",
                is_active=True
            )

            db.add(admin_user)
            db.commit()

            print("=" * 50)
            print("默认管理员用户已创建:")
            print(f"用户名: admin")
            print(f"密码: {admin_password}")
            print(f"邮箱: <EMAIL>")
            print("请首次登录后立即修改密码！")
            print("=" * 50)
        else:
            print("管理员用户已存在，跳过创建")

    except Exception as e:
        print(f"初始化管理员用户失败: {str(e)}")
        db.rollback()
    finally:
        db.close()


def main():
    """主函数"""
    print("开始初始化系统数据...")

    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成")

    # 初始化管理员用户
    init_admin_user()

    # 初始化系统设置
    init_system_settings()

    # 初始化邮件模板
    init_email_templates()

    print("系统数据初始化完成！")


if __name__ == "__main__":
    main()
