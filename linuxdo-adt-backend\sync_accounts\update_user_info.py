#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户信息更新脚本
功能：
1. 读取 user_api_info.txt 文件
2. 调用系统接口更新用户的等级信息和API Key
3. 跳过不存在的用户名
"""

import os
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple


class UserInfoUpdater:
    def __init__(self, base_url: str = "http://localhost:8000", 
                 username: str = "admin", password: str = "admin123"):
        """
        初始化用户信息更新器
        
        Args:
            base_url: API 基础URL
            username: 管理员用户名
            password: 管理员密码
        """
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.token_file = "token_cache.json"
        self.user_api_info_file = "user_api_info.txt"
        self.token_data = None
        
    def load_token_cache(self) -> Optional[Dict]:
        """从缓存文件加载token"""
        try:
            if os.path.exists(self.token_file):
                with open(self.token_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 检查token是否过期（提前5分钟刷新）
                    expire_time = datetime.fromisoformat(data['expire_time'])
                    if datetime.now() + timedelta(minutes=5) < expire_time:
                        return data
        except Exception as e:
            print(f"加载token缓存失败: {e}")
        return None
    
    def save_token_cache(self, token: str, expire_minutes: int = 30):
        """保存token到缓存文件"""
        try:
            expire_time = datetime.now() + timedelta(minutes=expire_minutes)
            data = {
                'token': token,
                'expire_time': expire_time.isoformat(),
                'created_at': datetime.now().isoformat()
            }
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            self.token_data = data
            print(f"Token已保存，过期时间: {expire_time.strftime('%Y-%m-%d %H:%M:%S')}")
        except Exception as e:
            print(f"保存token缓存失败: {e}")
    
    def get_valid_token(self) -> Optional[str]:
        """获取有效的token"""
        # 先尝试从缓存加载
        cached_token = self.load_token_cache()
        if cached_token:
            self.token_data = cached_token
            print("使用缓存的token")
            return cached_token['token']
        
        # 缓存无效，重新登录获取token
        return self.login_and_get_token()
    
    def login_and_get_token(self) -> Optional[str]:
        """登录并获取新的token"""
        try:
            print("正在登录获取新token...")
            login_url = f"{self.base_url}/api/auth/login"
            
            # 准备登录数据
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            # 发送登录请求
            response = requests.post(login_url, data=login_data)
            response.raise_for_status()
            
            result = response.json()
            token = result.get('access_token')
            
            if token:
                # 保存token到缓存
                self.save_token_cache(token)
                print("登录成功，获取到新token")
                return token
            else:
                print("登录响应中没有找到token")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"登录请求失败: {e}")
            return None
        except Exception as e:
            print(f"登录过程出错: {e}")
            return None
    
    def load_user_api_info(self) -> List[Tuple[str, str, str]]:
        """加载用户API信息文件"""
        user_info_list = []
        try:
            if not os.path.exists(self.user_api_info_file):
                print(f"文件 {self.user_api_info_file} 不存在")
                return user_info_list
            
            with open(self.user_api_info_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        # CSV格式: username,trust_level,api_key
                        parts = line.split(',')
                        if len(parts) >= 3:
                            username = parts[0].strip()
                            trust_level = parts[1].strip()
                            api_key = parts[2].strip()
                            user_info_list.append((username, trust_level, api_key))
                        else:
                            print(f"第{line_num}行格式错误，跳过: {line}")
            
            print(f"从文件中加载了 {len(user_info_list)} 个用户信息")
            return user_info_list
            
        except Exception as e:
            print(f"加载用户API信息文件失败: {e}")
            return user_info_list
    
    def get_account_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取账号信息"""
        token = self.get_valid_token()
        if not token:
            print("无法获取有效token")
            return None
        
        try:
            accounts_url = f"{self.base_url}/api/accounts"
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(accounts_url, headers=headers)
            response.raise_for_status()
            
            accounts = response.json()
            
            # 查找匹配的用户名
            for account in accounts:
                if account.get('username') == username:
                    return account
            
            return None
            
        except requests.exceptions.RequestException as e:
            print(f"获取账号信息失败: {e}")
            return None
        except Exception as e:
            print(f"处理账号信息时出错: {e}")
            return None
    
    def update_account_info(self, account_id: int, level_info: str, key_info: str) -> bool:
        """更新账号的等级信息和Key信息"""
        token = self.get_valid_token()
        if not token:
            print("无法获取有效token")
            return False
        
        try:
            # 先获取当前账号信息
            account_url = f"{self.base_url}/api/accounts/{account_id}"
            
            headers = {
                'Authorization': f'Bearer {token}',
                'Content-Type': 'application/json'
            }
            
            # 获取当前账号详细信息
            response = requests.get(account_url, headers=headers)
            response.raise_for_status()
            current_account = response.json()
            
            # 准备更新数据（保持其他字段不变，只更新等级和Key）
            update_data = {
                'username': current_account.get('username'),
                'password': current_account.get('password'),
                'email': current_account.get('email'),
                'level_info': level_info,  # 更新等级信息
                'key_info': key_info,      # 更新Key信息
                'need_notification': current_account.get('need_notification', True)
            }
            
            # 发送更新请求
            response = requests.put(account_url, json=update_data, headers=headers)
            response.raise_for_status()
            
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"更新账号信息失败: {e}")
            return False
        except Exception as e:
            print(f"更新账号信息时出错: {e}")
            return False
    
    def run_update(self):
        """执行用户信息更新操作"""
        print("=" * 60)
        print(f"开始用户信息更新 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        try:
            # 加载用户API信息
            user_info_list = self.load_user_api_info()
            
            if not user_info_list:
                print("没有找到需要更新的用户信息")
                return
            
            success_count = 0
            skip_count = 0
            error_count = 0
            
            for username, trust_level, api_key in user_info_list:
                print(f"\n处理用户: {username}")
                
                # 获取账号信息
                account = self.get_account_by_username(username)
                
                if not account:
                    print(f"  ❌ 用户 {username} 不存在，跳过")
                    skip_count += 1
                    continue
                
                # 更新账号信息
                account_id = account.get('id')
                if self.update_account_info(account_id, trust_level, api_key):
                    print(f"  ✅ 用户 {username} 更新成功")
                    print(f"     等级: {trust_level}")
                    print(f"     API Key: {api_key[:20]}...")
                    success_count += 1
                else:
                    print(f"  ❌ 用户 {username} 更新失败")
                    error_count += 1
            
            print("\n" + "=" * 60)
            print("更新完成统计:")
            print(f"  成功更新: {success_count} 个用户")
            print(f"  跳过用户: {skip_count} 个用户")
            print(f"  更新失败: {error_count} 个用户")
            print("=" * 60)
                
        except Exception as e:
            print(f"更新过程中出错: {e}")


def main():
    """主函数"""
    # 配置信息 - 请根据实际情况修改
    config = {
        'base_url': 'http://localhost:8000',
        'username': 'admin',
        'password': 'admin123'
    }
    
    # 创建更新器
    updater = UserInfoUpdater(**config)
    
    # 执行更新
    updater.run_update()


if __name__ == "__main__":
    main()
