<template>
  <div class="task-list">
    <div class="header-actions">
      <h3>任务管理</h3>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建任务
      </el-button>
    </div>
    
    <el-table :data="tasks" v-loading="loading" style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="title" label="任务标题" min-width="200" show-overflow-tooltip />
      <el-table-column prop="task_type" label="任务类型" width="120" />
      <el-table-column prop="duration_days" label="代挂天数" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" width="180">
        <template #default="scope">
          {{ formatEndDate(scope.row.created_at, scope.row.duration_days) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="300" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="copyShareLink(scope.row)">
            <el-icon><Share /></el-icon>
            复制分享链接
          </el-button>
          <el-button size="small" type="warning" @click="regenerateToken(scope.row)">
            <el-icon><Refresh /></el-icon>
            重新生成
          </el-button>
          <el-button size="small" type="danger" @click="deleteTask(scope.row)">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 创建任务对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建任务" width="500px">
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="100px">
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input v-model="taskForm.description" type="textarea" placeholder="请输入任务描述" />
        </el-form-item>
        <el-form-item label="任务类型" prop="task_type">
          <el-select v-model="taskForm.task_type" placeholder="请选择任务类型">
            <el-option label="升2级" value="升2级" />
            <el-option label="升3级" value="升3级" />
            <el-option label="保号" value="保号" />
          </el-select>
        </el-form-item>
        <el-form-item label="代挂天数" prop="duration_days">
          <el-input-number v-model="taskForm.duration_days" :min="1" :max="365" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="handleCreateTask" :loading="creating">创建</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTasks, createTask, deleteTask as deleteTaskApi, regenerateShareToken } from '../api/tasks'
import type { Task, TaskCreate } from '../api/tasks'

const tasks = ref<Task[]>([])
const loading = ref(false)
const showCreateDialog = ref(false)
const creating = ref(false)

const taskForm = reactive<TaskCreate>({
  title: '',
  description: '',
  task_type: '',
  duration_days: 1
})

const taskRules = {
  title: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
  task_type: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
  duration_days: [{ required: true, message: '请输入代挂天数', trigger: 'blur' }]
}

const taskFormRef = ref()

const loadTasks = async () => {
  loading.value = true
  try {
    const response = await getTasks()
    tasks.value = response.data
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const handleCreateTask = async () => {
  if (!taskFormRef.value) return
  
  await taskFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      creating.value = true
      try {
        await createTask(taskForm)
        ElMessage.success('任务创建成功')
        showCreateDialog.value = false
        Object.assign(taskForm, { title: '', description: '', task_type: '', duration_days: 1 })
        loadTasks()
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '创建任务失败')
      } finally {
        creating.value = false
      }
    }
  })
}

const copyShareLink = (task: Task) => {
  const shareUrl = `${window.location.origin}/share/${task.share_token}`
  navigator.clipboard.writeText(shareUrl).then(() => {
    ElMessage.success('分享链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

const regenerateToken = async (task: Task) => {
  try {
    await regenerateShareToken(task.id)
    ElMessage.success('分享链接已重新生成')
    loadTasks()
  } catch (error) {
    ElMessage.error('重新生成失败')
  }
}

const deleteTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteTaskApi(task.id)
    ElMessage.success('任务删除成功')
    loadTasks()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '待接单': 'info',
    '进行中': 'warning',
    '已完成': 'success',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatEndDate = (createdAt: string, durationDays: number) => {
  const startDate = new Date(createdAt)
  const endDate = new Date(startDate.getTime() + durationDays * 24 * 60 * 60 * 1000)
  return endDate.toLocaleString('zh-CN')
}

onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.task-list {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions h3 {
  margin: 0;
}
</style>
