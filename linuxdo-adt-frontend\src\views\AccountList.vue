<template>
  <div class="account-list">
    <div class="header-actions">
      <h3>账号管理</h3>
    </div>
    
    <el-table :data="accounts" v-loading="loading" style="width: 100%">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="task_id" label="任务ID" width="100" />
      <el-table-column prop="username" label="LinuxDo用户名" width="150" show-overflow-tooltip />
      <el-table-column prop="password" label="LinuxDo密码" width="150" show-overflow-tooltip />
      <el-table-column prop="email" label="LinuxDo邮箱" min-width="200" show-overflow-tooltip />
      <el-table-column prop="level_info" label="等级信息" width="120" />
      <el-table-column prop="key_info" label="Key信息" width="150" />
      <el-table-column prop="need_notification" label="通知设置" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.need_notification ? 'success' : 'info'">
            {{ scope.row.need_notification ? '接收通知' : '不接收通知' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="120">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="submitted_at" label="提交时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.submitted_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="280" fixed="right">
        <template #default="scope">
          <el-button size="small" type="primary" @click="editAccount(scope.row)">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
          <el-select
            v-model="scope.row.status"
            @change="updateStatus(scope.row)"
            size="small"
            style="width: 120px; margin-left: 10px"
          >
            <el-option label="已提交" value="已提交" />
            <el-option label="处理中" value="处理中" />
            <el-option label="已完成" value="已完成" />
          </el-select>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑账号信息对话框 -->
    <el-dialog v-model="showEditDialog" title="编辑账号信息" width="500px">
      <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="120px">
        <el-form-item label="LinuxDo用户名" prop="username">
          <el-input v-model="editForm.username" placeholder="请输入LinuxDo用户名" />
        </el-form-item>
        <el-form-item label="LinuxDo密码" prop="password">
          <el-input v-model="editForm.password" type="password" placeholder="请输入LinuxDo密码" show-password />
        </el-form-item>
        <el-form-item label="LinuxDo邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入LinuxDo邮箱" />
        </el-form-item>
        <el-form-item label="等级信息">
          <el-input v-model="editForm.level_info" placeholder="请输入等级信息（可选）" />
        </el-form-item>
        <el-form-item label="Key信息">
          <el-input v-model="editForm.key_info" placeholder="请输入Key信息（可选）" />
        </el-form-item>
        <el-form-item label="通知设置">
          <el-switch
            v-model="editForm.need_notification"
            active-text="接收通知"
            inactive-text="不接收通知"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleUpdateAccount" :loading="updating">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../api'

interface AccountSubmission {
  id: number
  task_id: number
  username: string
  password: string
  email: string
  level_info: string
  key_info: string
  need_notification: boolean
  submitted_at: string
  status: string
}

const accounts = ref<AccountSubmission[]>([])
const loading = ref(false)
const showEditDialog = ref(false)
const updating = ref(false)
const currentEditingAccount = ref<AccountSubmission | null>(null)

const editForm = reactive({
  id: 0,
  username: '',
  password: '',
  email: '',
  level_info: '',
  key_info: '',
  need_notification: true
})

const editRules = {
  username: [{ required: true, message: '请输入LinuxDo用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入LinuxDo密码', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入LinuxDo邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

const editFormRef = ref()

const loadAccounts = async () => {
  loading.value = true
  try {
    const response = await api.get<AccountSubmission[]>('/api/accounts')
    accounts.value = response.data
  } catch (error) {
    ElMessage.error('加载账号列表失败')
  } finally {
    loading.value = false
  }
}

const updateStatus = async (account: AccountSubmission) => {
  try {
    await api.put(`/api/accounts/${account.id}/status`, null, {
      params: { status: account.status }
    })
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
    loadAccounts() // 重新加载数据
  }
}

const editAccount = (account: AccountSubmission) => {
  currentEditingAccount.value = account
  Object.assign(editForm, {
    id: account.id,
    username: account.username,
    password: account.password,
    email: account.email,
    level_info: account.level_info,
    key_info: account.key_info,
    need_notification: account.need_notification
  })
  showEditDialog.value = true
}

const handleUpdateAccount = async () => {
  if (!editFormRef.value) return

  await editFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      updating.value = true
      try {
        await api.put(`/api/accounts/${editForm.id}`, {
          username: editForm.username,
          password: editForm.password,
          email: editForm.email,
          level_info: editForm.level_info,
          key_info: editForm.key_info,
          need_notification: editForm.need_notification
        })
        ElMessage.success('账号信息更新成功')
        showEditDialog.value = false
        loadAccounts() // 重新加载数据
      } catch (error: any) {
        ElMessage.error(error.response?.data?.detail || '更新失败')
      } finally {
        updating.value = false
      }
    }
  })
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已提交': 'info',
    '处理中': 'warning',
    '已完成': 'success'
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  loadAccounts()
})
</script>

<style scoped>
.account-list {
  background: white;
  padding: 20px;
  border-radius: 8px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions h3 {
  margin: 0;
}
</style>
