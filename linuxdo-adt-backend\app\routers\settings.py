from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from ..database import get_db
from ..models import SystemSettings, EmailTemplate, EmailLog, User
from ..schemas import (
    SystemSettingsCreate, SystemSettingsUpdate, SystemSettingsResponse,
    EmailTemplateCreate, EmailTemplateUpdate, EmailTemplateResponse,
    EmailLogResponse, EmailSendRequest
)
from typing import Dict
from ..auth import get_current_user
from ..email_service import email_service

router = APIRouter(prefix="/api/settings", tags=["系统设置"])


def check_admin_permission(current_user: User = Depends(get_current_user)):
    """检查管理员权限"""
    if current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user


# 系统设置相关
@router.get("/system", response_model=List[SystemSettingsResponse])
def get_system_settings(
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取所有系统设置"""
    settings = db.query(SystemSettings).all()
    return settings


@router.put("/system/batch")
def batch_update_settings(
    settings: Dict[str, str],
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """批量更新系统设置"""
    print(f"Received settings: {settings}")  # 调试日志
    updated_count = 0

    for key, value in settings.items():
        print(f"Processing: {key} = {value} (type: {type(value)})")  # 调试日志
        setting = db.query(SystemSettings).filter(SystemSettings.key == key).first()

        if setting:
            # 更新现有设置
            setting.value = str(value)  # 确保转换为字符串
            updated_count += 1
        else:
            # 创建新设置
            new_setting = SystemSettings(
                key=key,
                value=str(value),  # 确保转换为字符串
                description=f"自动创建的设置项: {key}"
            )
            db.add(new_setting)
            updated_count += 1

    db.commit()
    return {"message": f"已更新 {updated_count} 个设置项"}


@router.get("/system/{key}", response_model=SystemSettingsResponse)
def get_system_setting(
    key: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取指定的系统设置"""
    setting = db.query(SystemSettings).filter(SystemSettings.key == key).first()
    if not setting:
        raise HTTPException(status_code=404, detail="设置项不存在")
    return setting


@router.post("/system", response_model=SystemSettingsResponse)
def create_system_setting(
    setting: SystemSettingsCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """创建系统设置"""
    # 检查key是否已存在
    existing = db.query(SystemSettings).filter(SystemSettings.key == setting.key).first()
    if existing:
        raise HTTPException(status_code=400, detail="设置项已存在")
    
    db_setting = SystemSettings(**setting.dict())
    db.add(db_setting)
    db.commit()
    db.refresh(db_setting)
    return db_setting


@router.put("/system/{key}", response_model=SystemSettingsResponse)
def update_system_setting(
    key: str,
    setting_update: SystemSettingsUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """更新系统设置"""
    setting = db.query(SystemSettings).filter(SystemSettings.key == key).first()
    if not setting:
        raise HTTPException(status_code=404, detail="设置项不存在")
    
    for field, value in setting_update.dict(exclude_unset=True).items():
        setattr(setting, field, value)
    
    db.commit()
    db.refresh(setting)
    return setting


@router.delete("/system/{key}")
def delete_system_setting(
    key: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """删除系统设置"""
    setting = db.query(SystemSettings).filter(SystemSettings.key == key).first()
    if not setting:
        raise HTTPException(status_code=404, detail="设置项不存在")

    db.delete(setting)
    db.commit()
    return {"message": "设置项已删除"}


# 获取注册开关状态（公开接口）
@router.get("/registration-enabled")
def get_registration_status(db: Session = Depends(get_db)):
    """获取注册开关状态（公开接口）"""
    setting = db.query(SystemSettings).filter(SystemSettings.key == "registration_enabled").first()
    if not setting:
        # 默认不允许注册
        return {"enabled": False}
    return {"enabled": setting.value.lower() == "true"}


# 获取首页显示设置（公开接口）
@router.get("/homepage-enabled")
def get_homepage_status(db: Session = Depends(get_db)):
    """获取首页显示设置（公开接口）"""
    setting = db.query(SystemSettings).filter(SystemSettings.key == "show_homepage").first()
    if not setting:
        # 默认不显示首页
        return {"enabled": False}
    return {"enabled": setting.value.lower() == "true"}


# 邮件模板相关
@router.get("/email-templates", response_model=List[EmailTemplateResponse])
def get_email_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取所有邮件模板"""
    templates = db.query(EmailTemplate).all()
    return templates


@router.get("/email-templates/{template_id}", response_model=EmailTemplateResponse)
def get_email_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取指定邮件模板"""
    template = db.query(EmailTemplate).filter(EmailTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")
    return template


@router.post("/email-templates", response_model=EmailTemplateResponse)
def create_email_template(
    template: EmailTemplateCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """创建邮件模板"""
    # 检查模板名称是否已存在
    existing = db.query(EmailTemplate).filter(EmailTemplate.name == template.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="模板名称已存在")
    
    db_template = EmailTemplate(**template.dict())
    db.add(db_template)
    db.commit()
    db.refresh(db_template)
    return db_template


@router.put("/email-templates/{template_id}", response_model=EmailTemplateResponse)
def update_email_template(
    template_id: int,
    template_update: EmailTemplateUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """更新邮件模板"""
    template = db.query(EmailTemplate).filter(EmailTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")
    
    for field, value in template_update.dict(exclude_unset=True).items():
        setattr(template, field, value)
    
    db.commit()
    db.refresh(template)
    return template


@router.delete("/email-templates/{template_id}")
def delete_email_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """删除邮件模板"""
    template = db.query(EmailTemplate).filter(EmailTemplate.id == template_id).first()
    if not template:
        raise HTTPException(status_code=404, detail="邮件模板不存在")
    
    db.delete(template)
    db.commit()
    return {"message": "邮件模板已删除"}


# 邮件日志相关
@router.get("/email-logs", response_model=List[EmailLogResponse])
def get_email_logs(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """获取邮件发送日志"""
    logs = db.query(EmailLog).offset(skip).limit(limit).all()
    return logs


# 发送测试邮件
@router.post("/send-test-email")
async def send_test_email(
    email_request: EmailSendRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission)
):
    """发送测试邮件"""
    success = await email_service.send_email(
        to_email=email_request.to_email,
        subject=email_request.subject,
        content=email_request.content,
        template_name=email_request.template_name,
        db=db
    )
    
    if success:
        return {"message": "测试邮件发送成功"}
    else:
        raise HTTPException(status_code=500, detail="邮件发送失败")
