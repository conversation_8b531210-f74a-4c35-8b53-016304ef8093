#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号同步脚本配置文件
"""

# API 配置
API_CONFIG = {
    'base_url': 'http://localhost:8000',  # API 基础URL
    'username': 'admin',                  # 管理员用户名
    'password': 'admin123'                # 管理员密码
}

# 文件配置
FILE_CONFIG = {
    'token_cache_file': 'token_cache.json',  # Token缓存文件
    'user_info_file': 'user_info.txt'        # 用户信息文件
}

# Token 配置
TOKEN_CONFIG = {
    'expire_minutes': 30,      # Token过期时间（分钟）
    'refresh_before_minutes': 5  # 提前刷新时间（分钟）
}

# 日志配置
LOG_CONFIG = {
    'enable_debug': False,     # 是否启用调试日志
    'log_file': None          # 日志文件路径（None表示不写入文件）
}
