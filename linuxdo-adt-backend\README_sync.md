# 账号同步脚本使用说明

## 功能概述

这个脚本用于自动同步 LinuxDo ADT 系统中的账号信息，并维护一个 `user_info.txt` 文件。

### 主要功能

1. **自动Token管理**：自动获取和缓存API访问token，过期时自动刷新
2. **账号信息同步**：从系统API获取所有账号信息
3. **智能文件维护**：
   - 系统中"进行中"状态的账号会被添加到文件中
   - 系统中非"进行中"状态的账号会从文件中删除
   - 文件中存在但系统中不存在的账号会被保留（防止误删）

## 文件结构

```
linuxdo-adt-backend/
├── sync_accounts.py         # 主脚本文件
├── config_sync.py          # 配置文件
├── README_sync.md          # 使用说明
├── user_info_example.txt   # 示例文件格式
├── token_cache.json        # Token缓存文件（自动生成）
└── user_info.txt           # 用户信息文件（自动生成/更新）
```

## 使用方法

### 1. 配置设置

编辑 `config_sync.py` 文件，修改以下配置：

```python
API_CONFIG = {
    'base_url': 'http://localhost:8000',  # 修改为实际的API地址
    'username': 'admin',                  # 修改为实际的管理员用户名
    'password': 'admin123'                # 修改为实际的管理员密码
}
```

### 2. 安装依赖

```bash
pip install requests
```

### 3. 运行脚本

```bash
# 进入后端目录
cd linuxdo-adt-backend

# 运行同步脚本
python sync_accounts.py
```

### 4. 定时运行（可选）

可以使用系统的定时任务来定期运行脚本：

#### Linux/macOS (crontab)
```bash
# 编辑crontab
crontab -e

# 添加定时任务（每小时运行一次）
0 * * * * cd /path/to/linuxdo-adt-backend && python sync_accounts.py
```

#### Windows (任务计划程序)
1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器（如每小时运行一次）
4. 设置操作：启动程序 `python`，参数 `sync_accounts.py`，起始于脚本目录

## 输出文件格式

### user_info.txt 格式

```
# 账号信息文件
# 更新时间: 2024-01-01 12:00:00
# 格式: username:password:email:level_info:key_info:need_notification
# 总计: 5 个账号
#
testuser1:password123:<EMAIL>:2级:key123:True
testuser2:password456:<EMAIL>:3级:key456:False
```

### token_cache.json 格式

```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expire_time": "2024-01-01T13:00:00.000000",
  "created_at": "2024-01-01T12:30:00.000000"
}
```

## 运行日志示例

```
==================================================
开始账号同步 - 2024-01-01 12:00:00
==================================================
使用缓存的token
正在获取账号信息...
获取到 10 个账号
从现有文件中加载了 8 个用户
系统中有 10 个账号，其中 6 个进行中
将保留 9 个用户
移除了 2 个非进行中状态的账号
user_info.txt 文件已更新
同步完成
==================================================
```

## 同步逻辑说明

脚本的同步逻辑如下：

1. **获取系统账号**：通过API获取所有账号信息
2. **分类处理**：
   - `进行中状态账号`：需要保留在文件中
   - `非进行中状态账号`：需要从文件中删除
   - `文件中已存在但系统中不存在的账号`：保留（防止误删）

3. **文件更新**：
   - 添加系统中所有"进行中"状态的账号
   - 保留文件中存在但系统中不存在的账号
   - 移除系统中存在但状态非"进行中"的账号

## 注意事项

1. **权限要求**：脚本需要使用管理员账号才能访问账号信息API
2. **网络连接**：确保脚本运行环境能够访问API服务器
3. **文件权限**：确保脚本有权限读写工作目录中的文件
4. **备份建议**：建议定期备份 `user_info.txt` 文件
5. **安全性**：配置文件中包含密码，请妥善保管

## 故障排除

### 常见问题

1. **登录失败**
   - 检查用户名和密码是否正确
   - 确认API服务器是否正常运行

2. **Token过期**
   - 脚本会自动处理token过期，重新登录获取新token

3. **网络连接问题**
   - 检查API服务器地址是否正确
   - 确认网络连接是否正常

4. **文件权限问题**
   - 确保脚本有权限读写当前目录
   - 检查文件是否被其他程序占用

### 调试模式

如需启用详细日志，可以修改 `config_sync.py` 中的 `LOG_CONFIG`：

```python
LOG_CONFIG = {
    'enable_debug': True,      # 启用调试日志
    'log_file': 'sync.log'     # 写入日志文件
}
```
